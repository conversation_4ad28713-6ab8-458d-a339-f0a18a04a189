"use client"

import { LeftSidebar } from "./LeftSidebar"
import { MainCanvas } from "./MainCanvas"
import { OutermostSidebar } from "./OutermostSidebar"
import { RightSidebar } from "./RightSidebar"
import { TopToolbar } from "./TopToolbar"

export function EditorLayout() {
  return (
    <div className="h-full w-full flex flex-col bg-gray-900">
      {/* Top Toolbar */}
      <TopToolbar />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Outermost Sidebar - Fixed positioned, overlaps content */}
        <OutermostSidebar />

        {/* Content area that can be overlapped by outermost sidebar */}
        <div className="flex flex-1 overflow-hidden">
          {/* Tools/Blocks Sidebar - Can be overlapped */}
          <LeftSidebar />

          {/* Main Canvas */}
          <div className="flex-1 relative">
            <MainCanvas />
          </div>

          {/* Right Sidebar */}
          <RightSidebar />
        </div>
      </div>
    </div>
  )
}
