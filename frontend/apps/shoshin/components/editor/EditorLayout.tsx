"use client"

import { LeftSidebar } from "./LeftSidebar"
import { MainCanvas } from "./MainCanvas"
import { OutermostSidebar } from "./OutermostSidebar"
import { RightSidebar } from "./RightSidebar"
import { TopToolbar } from "./TopToolbar"

export function EditorLayout() {
  return (
    <div className="h-full w-full flex flex-col bg-gray-900">
      {/* Top Toolbar */}
      <TopToolbar />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Outermost Sidebar - Always on top */}
        <OutermostSidebar />

        {/* Tools/Blocks Sidebar - Can be overlapped */}
        <LeftSidebar />

        {/* Main Canvas */}
        <div className="flex-1 relative">
          <MainCanvas />
        </div>

        {/* Right Sidebar */}
        <RightSidebar />
      </div>
    </div>
  )
}
