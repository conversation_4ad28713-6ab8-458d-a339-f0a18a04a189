"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import {
  Brain,
  ChevronLeft,
  ChevronRight,
  Code,
  Database,
  GitBranch,
  Globe,
  MessageSquare,
  RotateCcw,
  Route,
  Search,
  User,
  Workflow
} from "lucide-react"
import { useState } from "react"
import { BlockItem } from "./BlockItem"

const blockCategories = [
  {
    title: "Blocks",
    items: [
      { id: "agent", name: "Agent", description: "Build an agent", icon: User, color: "bg-purple-600" },
      { id: "api", name: "API", description: "Use any API", icon: Globe, color: "bg-blue-600" },
      { id: "condition", name: "Condition", description: "Add a condition", icon: GitBranch, color: "bg-orange-600" },
      { id: "function", name: "Function", description: "Run custom logic", icon: Code, color: "bg-red-600" },
      { id: "router", name: "Router", description: "Route workflow", icon: Route, color: "bg-green-600" },
      { id: "memory", name: "Memory", description: "Add memory store", icon: Database, color: "bg-pink-600" },
      { id: "knowledge", name: "Knowledge", description: "Use vector search", icon: Brain, color: "bg-teal-600" },
      { id: "workflow", name: "Workflow", description: "Execute another workflow", icon: Workflow, color: "bg-amber-600" },
      { id: "response", name: "Response", description: "Send structured API response", icon: MessageSquare, color: "bg-blue-500" },
      { id: "loop", name: "Loop", description: "", icon: RotateCcw, color: "bg-cyan-600" }
    ]
  },
  {
    title: "Tools",
    items: []
  }
]

export function LeftSidebar() {
  const [activeTab, setActiveTab] = useState("Blocks")
  const [isCollapsed, setIsCollapsed] = useState(false)

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  return (
    <TooltipProvider>
      <div className={cn(
        "bg-gray-800 border-r border-gray-700 flex flex-col shadow-lg transition-all duration-300 ease-in-out relative z-10",
        isCollapsed ? "w-16" : "w-64"
      )}>
        {/* Header with collapse button */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-end">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="text-gray-400 hover:text-white hover:bg-gray-700 p-1 h-8 w-8"
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4" />
              ) : (
                <ChevronLeft className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Search */}
        {!isCollapsed && (
          <div className="p-4 border-b border-gray-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full bg-gray-700 text-white placeholder-gray-400 pl-10 pr-3 py-2 rounded-md border border-gray-600 focus:border-purple-500 focus:outline-none text-sm transition-colors"
              />
            </div>
          </div>
        )}

        {/* Tabs */}
        {!isCollapsed && (
          <div className="flex border-b border-gray-700">
            {blockCategories.map((category) => (
              <button
                key={category.title}
                onClick={() => setActiveTab(category.title)}
                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === category.title
                    ? "text-white bg-gray-700 border-b-2 border-purple-500"
                    : "text-gray-400 hover:text-white"
                }`}
              >
                {category.title}
              </button>
            ))}
          </div>
        )}

        {/* Block List */}
        <div className="flex-1 overflow-y-auto p-4 space-y-2">
          {!isCollapsed && (
            // Only show blocks when expanded
            blockCategories
              .find((cat) => cat.title === activeTab)
              ?.items.map((block) => (
                <BlockItem
                  key={block.id}
                  id={block.id}
                  name={block.name}
                  description={block.description}
                  icon={block.icon}
                  color={block.color}
                />
              ))
          )}
        </div>

        {/* Bottom section */}
        <div className="p-4 border-t border-gray-700">
          {!isCollapsed ? (
            // Show "Close Toolbar" button when expanded
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="w-full text-gray-400 hover:text-white hover:bg-gray-700 justify-start"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Close Toolbar
            </Button>
          ) : (
            // Show "Open Toolbar" button when collapsed
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSidebar}
                  className="w-full text-gray-400 hover:text-white hover:bg-gray-700 p-2"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                Open Toolbar
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </TooltipProvider>
  )
}
